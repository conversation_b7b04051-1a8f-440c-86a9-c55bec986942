
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Diensten from "./pages/Diensten";
import Tarieven from "./pages/Tarieven";
import Beoordelingen from "./pages/Beoordelingen";
import Contact from "./pages/Contact";
import OverOns from "./pages/OverOns";
import Projecten from "./pages/Projecten";
import AlgemeneVoorwaarden from "./pages/AlgemeneVoorwaarden";
import KlusPlaatsen from "./pages/KlusPlaatsen";
import AfspraakMaken from "./pages/AfspraakMaken";
import Offerte from "./pages/Offerte";
import NotFound from "./pages/NotFound";
import LoodgietersWerk from "./pages/diensten/LoodgietersWerk";
import Elektriciteit from "./pages/diensten/Elektriciteit";
import Schilderwerk from "./pages/diensten/Schilderwerk";
import Montage from "./pages/diensten/Montage";
import Onderhoud from "./pages/diensten/Onderhoud";
import Tegels from "./pages/diensten/Tegels";
import Tuinonderhoud from "./pages/diensten/Tuinonderhoud";
import Verhuizing from "./pages/diensten/Verhuizing";
import Schoonmaak from "./pages/diensten/Schoonmaak";
import Loodgieter from "./pages/diensten/Loodgieter";
import Elektricien from "./pages/diensten/Elektricien";
import Klusjesman from "./pages/diensten/Klusjesman";
import CvMonteur from "./pages/diensten/CvMonteur";
import WitgoedReparatie from "./pages/diensten/WitgoedReparatie";
import Dakdekker from "./pages/diensten/Dakdekker";
import SmartHome from "./pages/diensten/SmartHome";
import Timmerman from "./pages/diensten/Timmerman";
import Schilder from "./pages/diensten/Schilder";
import Behanger from "./pages/diensten/Behanger";
import Stukadoor from "./pages/diensten/Stukadoor";
import Hovenier from "./pages/diensten/Hovenier";
import Koeltechniek from "./pages/diensten/Koeltechniek";
import Slotenmaker from "./pages/diensten/Slotenmaker";
import Stratenmaker from "./pages/diensten/Stratenmaker";
import Ongediertebestrijder from "./pages/diensten/Ongediertebestrijder";
import IctSpecialist from "./pages/diensten/IctSpecialist";
import ComfortInstallateur from "./pages/diensten/ComfortInstallateur";
import Telefoonreparatie from "./pages/diensten/Telefoonreparatie";
import Isolatiebedrijf from "./pages/diensten/Isolatiebedrijf";
import Klushulp from "./pages/diensten/Klushulp";
import Glaszetter from "./pages/diensten/Glaszetter";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/diensten" element={<Diensten />} />
          <Route path="/diensten/loodgieterswerk" element={<LoodgietersWerk />} />
          <Route path="/diensten/elektriciteit" element={<Elektriciteit />} />
          <Route path="/diensten/schilderwerk" element={<Schilderwerk />} />
          <Route path="/diensten/montage" element={<Montage />} />
          <Route path="/diensten/onderhoud" element={<Onderhoud />} />
          <Route path="/diensten/tegels" element={<Tegels />} />
          <Route path="/diensten/tuinonderhoud" element={<Tuinonderhoud />} />
          <Route path="/diensten/verhuizing" element={<Verhuizing />} />
          <Route path="/diensten/schoonmaak" element={<Schoonmaak />} />
          <Route path="/diensten/loodgieter" element={<Loodgieter />} />
          <Route path="/diensten/elektricien" element={<Elektricien />} />
          <Route path="/diensten/klusjesman" element={<Klusjesman />} />
          <Route path="/diensten/cv-monteur" element={<CvMonteur />} />
          <Route path="/diensten/witgoed-reparatie" element={<WitgoedReparatie />} />
          <Route path="/diensten/dakdekker" element={<Dakdekker />} />
          <Route path="/diensten/smart-home" element={<SmartHome />} />
          <Route path="/diensten/timmerman" element={<Timmerman />} />
          <Route path="/diensten/schilder" element={<Schilder />} />
          <Route path="/diensten/behanger" element={<Behanger />} />
          <Route path="/diensten/stukadoor" element={<Stukadoor />} />
          <Route path="/diensten/hovenier" element={<Hovenier />} />
          <Route path="/diensten/koeltechniek" element={<Koeltechniek />} />
          <Route path="/diensten/slotenmaker" element={<Slotenmaker />} />
          <Route path="/diensten/stratenmaker" element={<Stratenmaker />} />
          <Route path="/diensten/ongediertebestrijder" element={<Ongediertebestrijder />} />
          <Route path="/diensten/ict-specialist" element={<IctSpecialist />} />
          <Route path="/diensten/comfort-installateur" element={<ComfortInstallateur />} />
          <Route path="/diensten/telefoonreparatie" element={<Telefoonreparatie />} />
          <Route path="/diensten/isolatiebedrijf" element={<Isolatiebedrijf />} />
          <Route path="/diensten/klushulp" element={<Klushulp />} />
          <Route path="/diensten/glaszetter" element={<Glaszetter />} />
          <Route path="/tarieven" element={<Tarieven />} />
          <Route path="/beoordelingen" element={<Beoordelingen />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/over-ons" element={<OverOns />} />
          <Route path="/projecten" element={<Projecten />} />
          <Route path="/algemene-voorwaarden" element={<AlgemeneVoorwaarden />} />
          <Route path="/klus-plaatsen" element={<KlusPlaatsen />} />
          <Route path="/offerte" element={<Offerte />} />
          <Route path="/afspraak-maken" element={<AfspraakMaken />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
