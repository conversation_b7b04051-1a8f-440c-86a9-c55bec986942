
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import {
  Shield,
  Zap,
  Award,
  Star,
  Wrench,
  Hammer,
  Zap as Electric,
  Paintbrush,
  Home,
  Thermometer,
  Droplets,
  Wifi,
  Leaf,
  Phone,
  Truck,
  Bug,
  Monitor,
  Settings,
  Snowflake,
  Lightbulb,
  Building,
  Palette,
  Hammer as Saw,
  Paintbrush as Brush,
  FileImage as Wallpaper,
  Wrench as Trowel,
  TreePine as Trees,
  Wind as AirVent,
  Key,
  Grid3X3 as Blocks,
  Shield as ShieldIcon,
  Cpu,
  Sun,
  Smartphone,
  HardHat,
  Move3D as Move,
  Sparkles as Glass,
  Search,
  Calculator,
  Map
} from 'lucide-react';
import { useEffect, useState } from 'react';

const Index = () => {
  const [currentReviewIndex, setCurrentReviewIndex] = useState(0);
  const [postcode, setPostcode] = useState('');
  const [searchResult, setSearchResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Google Maps API Key from environment variables
  const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

  // Service areas data
  const serviceAreas = {
    'Noord-Holland': [
      'Amsterdam', 'Haarlem', 'Alkmaar', 'Hilversum', 'Zaandam', 'Hoofddorp',
      'Purmerend', 'Beverwijk', 'Amstelveen', 'Heemskerk', 'Edam', 'Volendam',
      'Weesp', 'Bussum', 'Laren', 'Naarden', 'Zaandijk', 'Medemblik', 'Schoorl', 'Julianadorp'
    ],
    'Utrecht': [
      'Utrecht', 'Amersfoort', 'Veenendaal', 'Nieuwegein', 'Houten', 'Zeist',
      'Baarn', 'Bunnik', 'Driebergen-Rijsenburg', 'Wijk bij Duurstede', 'Leersum',
      'Culemborg', 'Rhenen', 'Soest', 'IJsselstein', 'Montfoort', 'Woerden', 'Oudewater'
    ],
    'Zuid-Holland': [
      'Rotterdam', 'Den Haag', 'Delft', 'Leiden', 'Dordrecht', 'Zoetermeer',
      'Schiedam', 'Vlaardingen', 'Alphen aan den Rijn', 'Gouda', 'Brielle',
      'Capelle aan den IJssel', 'Maassluis', 'Hellevoetsluis', 'Spijkenisse',
      'Rijnmond', 'Pijnacker-Nootdorp', 'Hoogvliet', 'Waddinxveen', 'Bleiswijk'
    ]
  };

  // Calculate travel costs based on actual distance
  const calculateTravelCosts = (distanceKm) => {
    if (distanceKm <= 10) {
      return { cost: 0, message: 'Geen voorrijkosten!' };
    } else if (distanceKm <= 25) {
      return { cost: 25, message: 'Voorrijkosten: €25 (excl. BTW)' };
    } else if (distanceKm <= 50) {
      return { cost: 50, message: 'Voorrijkosten: €50 (excl. BTW)' };
    } else if (distanceKm <= 100) {
      return { cost: 75, message: 'Voorrijkosten: €75 (excl. BTW)' };
    } else {
      return { cost: null, message: 'Buiten ons werkgebied (> 100 km)' };
    }
  };

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c; // Distance in kilometers
  };

  // Geocode address using Google Maps Geocoding API
  const geocodeAddress = async (address) => {
    try {
      // For demo purposes, using a mock geocoding service
      // In production, you would use Google Maps Geocoding API
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address + ', Netherlands')}&limit=1`);
      const data = await response.json();

      if (data && data.length > 0) {
        return {
          lat: parseFloat(data[0].lat),
          lng: parseFloat(data[0].lon),
          formatted_address: data[0].display_name
        };
      }
      return null;
    } catch (error) {
      console.error('Geocoding error:', error);
      return null;
    }
  };

  // Handle postcode search with real geocoding
  const handlePostcodeSearch = async () => {
    if (!postcode.trim()) return;

    setIsLoading(true);
    setSearchResult(null);

    try {
      // Mijdrecht coordinates (our base location)
      const mijdrechtLat = 52.2067;
      const mijdrechtLng = 4.8647;

      // Geocode the input address
      const location = await geocodeAddress(postcode);

      if (location) {
        // Calculate actual distance
        const distanceKm = calculateDistance(
          mijdrechtLat,
          mijdrechtLng,
          location.lat,
          location.lng
        );

        const travelInfo = calculateTravelCosts(distanceKm);
        const inServiceArea = distanceKm <= 100;

        setSearchResult({
          location: location.formatted_address.split(',')[0], // Get city name
          coordinates: { lat: location.lat, lng: location.lng },
          inServiceArea,
          distance: `${Math.round(distanceKm)} km`,
          distanceKm: Math.round(distanceKm),
          ...travelInfo
        });
      } else {
        setSearchResult({
          location: postcode,
          inServiceArea: false,
          cost: null,
          distance: 'Onbekend',
          message: 'Locatie niet gevonden. Controleer uw invoer.'
        });
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResult({
        location: postcode,
        inServiceArea: false,
        cost: null,
        distance: 'Fout',
        message: 'Er is een fout opgetreden. Probeer het opnieuw.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const whyChooseUs = [
    {
      icon: Shield,
      title: "100% Betrouwbaar & Verzekerd",
      description: "Gecertificeerde vakmannen met jarenlange ervaring",
      details: [
        "Volledig verzekerd en gecertificeerd",
        "Garantie op alle uitgevoerde werkzaamheden",
        "Transparante werkwijze zonder verrassingen",
        "Vaste tarieven bekend vooraf"
      ],
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: Zap,
      title: "Supersnel & Flexibel",
      description: "Snelle service wanneer u het nodig heeft",
      details: [
        "Binnen 24 uur reactie op uw aanvraag",
        "Spoedklussen binnen 4 uur mogelijk",
        "Flexibele planning ook 's avonds/weekend",
        "Direct telefonisch bereikbaar"
      ],
      color: "from-orange-500 to-orange-600"
    }
  ];

  const qualityService = [
    {
      icon: Award,
      title: "Premium Kwaliteit",
      description: "Vakmanschap dat u kunt vertrouwen",
      details: [
        "15+ jaar ervaring in alle vakgebieden",
        "4.9★ gemiddelde klantbeoordeling",
        "Gebruik van hoogwaardige materialen",
        "Nazorg en ondersteuning na oplevering"
      ],
      color: "from-green-500 to-green-600"
    },
    {
      icon: Star,
      title: "Eerlijke Prijzen",
      description: "Kwaliteit hoeft niet duur te zijn",
      details: [
        "Scherpe en concurrerende tarieven",
        "Gratis offerte en advies vooraf",
        "Geen verborgen kosten of toeslagen",
        "Betaling pas na volledige tevredenheid"
      ],
      color: "from-purple-500 to-purple-600"
    }
  ];

  const services = [
    { title: "Loodgieter", icon: Droplets, description: "Leidingen, kranen en sanitair", link: "/diensten/loodgieter", iconBg: "from-blue-100 to-cyan-100", iconColor: "text-blue-600" },
    { title: "Elektricien", icon: Electric, description: "Elektrische installaties en reparaties", link: "/diensten/elektricien", iconBg: "from-yellow-100 to-orange-100", iconColor: "text-yellow-600" },
    { title: "Klusjesman", icon: Hammer, description: "Algemene klussen en reparaties", link: "/diensten/klusjesman", iconBg: "from-orange-100 to-red-100", iconColor: "text-orange-600" },
    { title: "CV monteur", icon: Thermometer, description: "Verwarming en klimaatbeheersing", link: "/diensten/cv-monteur", iconBg: "from-red-100 to-pink-100", iconColor: "text-red-600" },
    { title: "Witgoed reparatie", icon: Settings, description: "Wasmachine, droger en vaatwasser", link: "/diensten/witgoed-reparatie", iconBg: "from-gray-100 to-slate-100", iconColor: "text-gray-600" },
    { title: "Dakdekker", icon: HardHat, description: "Dakbedekking en dakgoot onderhoud", link: "/diensten/dakdekker", iconBg: "from-green-100 to-emerald-100", iconColor: "text-green-600" },
    { title: "Smart Home", icon: Wifi, description: "Domotica en slimme installaties", link: "/diensten/smart-home", iconBg: "from-purple-100 to-violet-100", iconColor: "text-purple-600" },
    { title: "Timmerman", icon: Saw, description: "Houtwerk en meubelreparaties", link: "/diensten/timmerman", iconBg: "from-amber-100 to-yellow-100", iconColor: "text-amber-600" },
    { title: "Schilder", icon: Brush, description: "Binnen- en buitenschilderwerk", link: "/diensten/schilder", iconBg: "from-pink-100 to-rose-100", iconColor: "text-pink-600" },
    { title: "Behanger", icon: Wallpaper, description: "Behang en wandbekleding", link: "/diensten/behanger", iconBg: "from-fuchsia-100 to-purple-100", iconColor: "text-fuchsia-600" },
    { title: "Stukadoor", icon: Trowel, description: "Pleisterwerk en afwerking", link: "/diensten/stukadoor", iconBg: "from-stone-100 to-gray-100", iconColor: "text-stone-600" },
    { title: "Hovenier", icon: Trees, description: "Tuin onderhoud en aanleg", link: "/diensten/hovenier", iconBg: "from-lime-100 to-green-100", iconColor: "text-lime-600" },
    { title: "Koeltechniek", icon: AirVent, description: "Koeling en airconditioning", link: "/diensten/koeltechniek", iconBg: "from-sky-100 to-blue-100", iconColor: "text-sky-600" },
    { title: "Slotenmaker", icon: Key, description: "Sloten en beveiligingssystemen", link: "/diensten/slotenmaker", iconBg: "from-zinc-100 to-slate-100", iconColor: "text-zinc-600" },
    { title: "Stratenmaker", icon: Blocks, description: "Bestrating en tuinpaden", link: "/diensten/stratenmaker", iconBg: "from-neutral-100 to-stone-100", iconColor: "text-neutral-600" },
    { title: "Ongediertebestrijder", icon: ShieldIcon, description: "Pest control en preventie", link: "/diensten/ongediertebestrijder", iconBg: "from-emerald-100 to-teal-100", iconColor: "text-emerald-600" },
    { title: "ICT Specialist", icon: Cpu, description: "Computer en netwerk support", link: "/diensten/ict-specialist", iconBg: "from-indigo-100 to-blue-100", iconColor: "text-indigo-600" },
    { title: "Comfort Installateur", icon: Sun, description: "Comfort en energiebesparende systemen", link: "/diensten/comfort-installateur", iconBg: "from-yellow-100 to-amber-100", iconColor: "text-yellow-600" },
    { title: "Telefoonreparatie", icon: Smartphone, description: "Smartphone en tablet reparaties", link: "/diensten/telefoonreparatie", iconBg: "from-cyan-100 to-teal-100", iconColor: "text-cyan-600" },
    { title: "Isolatiebedrijf", icon: Home, description: "Isolatie en energiebesparing", link: "/diensten/isolatiebedrijf", iconBg: "from-slate-100 to-gray-100", iconColor: "text-slate-600" },
    { title: "Klushulp", icon: Move, description: "Verhuizing en transport hulp", link: "/diensten/klushulp", iconBg: "from-violet-100 to-purple-100", iconColor: "text-violet-600" },
    { title: "Glaszetter", icon: Glass, description: "Glas plaatsing en reparatie", link: "/diensten/glaszetter", iconBg: "from-teal-100 to-cyan-100", iconColor: "text-teal-600" }
  ];

  const reviews = [
    {
      name: "Maria van den Berg",
      text: "Uitstekend werk geleverd! Snel, netjes en voor een eerlijke prijs.",
      rating: 5
    },
    {
      name: "Jan Hendriks",
      text: "Zeer tevreden met de service. Punctueel en vakkundig.",
      rating: 5
    },
    {
      name: "Linda de Vries",
      text: "Aanrader! Professioneel en betrouwbaar.",
      rating: 5
    },
    {
      name: "Peter Jansen",
      text: "Fantastische service! Probleem snel opgelost en zeer vriendelijk.",
      rating: 5
    },
    {
      name: "Sandra Bakker",
      text: "Top kwaliteit werk. Precies wat ik nodig had. Zeker een aanrader!",
      rating: 5
    },
    {
      name: "Tom de Wit",
      text: "Snelle reactie en perfecte uitvoering. Heel tevreden!",
      rating: 5
    },
    {
      name: "Emma Visser",
      text: "Zeer professioneel en betrouwbaar. Komt afspraken na.",
      rating: 5
    },
    {
      name: "Mark Smit",
      text: "Uitstekende prijs-kwaliteit verhouding. Absoluut tevreden!",
      rating: 5
    }
  ];

  // Auto-scroll reviews carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentReviewIndex((prevIndex) =>
        prevIndex === reviews.length - 3 ? 0 : prevIndex + 1
      );
    }, 4000); // Change every 4 seconds

    return () => clearInterval(interval);
  }, [reviews.length]);

  // Get visible reviews (3 at a time)
  const getVisibleReviews = () => {
    const visibleReviews = [];
    for (let i = 0; i < 3; i++) {
      const index = (currentReviewIndex + i) % reviews.length;
      visibleReviews.push(reviews[index]);
    }
    return visibleReviews;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/10 to-accent-500/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="animate-fade-in">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Uw klus,
                <span className="text-primary-600"> onze expertise</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Van kleine reparaties tot grote verbouwingen - wij pakken elke klus 
                professioneel aan. Betrouwbaar, snel en tegen een eerlijke prijs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/klus-plaatsen">
                  <Button className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    Wat is uw klus?
                  </Button>
                </Link>
                <Link to="/afspraak-maken">
                  <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                    Direct een afspraak
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative animate-float">
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 border border-white/30">
                <img
                  src="/Handyman (1).png"
                  alt="Professionele klusman met gereedschap - meerdere armen met verschillende tools"
                  className="w-full h-80 object-cover rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-50">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 to-blue-100/30"></div>
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
              ⚡ Waarom 10.000+ klanten voor ons kiezen
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              De <span className="bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">slimste keuze</span> voor uw klus
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Ontdek waarom wij de nummer 1 klusservice zijn in de regio
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {whyChooseUs.map((feature, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                {/* Glassmorphism effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>

                <CardContent className="p-6 relative">
                  <div className="flex items-start space-x-4 mb-5">
                    <div className={`relative w-14 h-14 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                      <feature.icon className="w-7 h-7 text-white" />
                      {/* Glow effect */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} rounded-2xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-300`}></div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
                        <div className="w-2 h-2 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full mr-3 flex-shrink-0 group-hover:scale-125 transition-transform duration-300"></div>
                        <span className="font-medium">{detail}</span>
                      </div>
                    ))}
                  </div>

                  {/* Animated border */}
                  <div className={`absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r ${feature.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out`}></div>

                  {/* Corner accent */}
                  <div className="absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-primary-100 to-blue-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Quality & Service Section */}
      <section className="py-16 bg-gradient-to-br from-white via-gray-50/50 to-slate-50 relative">
        {/* Floating elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-gradient-to-br from-primary-200/30 to-blue-200/30 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-br from-accent-200/30 to-orange-200/30 rounded-full blur-xl"></div>

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 rounded-full text-sm font-medium mb-4">
              🏆 Premium kwaliteit & service
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">Vakmanschap</span> & Eerlijke Prijzen
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Kwaliteit hoeft niet duur te zijn. Ontdek onze unieke aanpak.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {qualityService.map((feature, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/90 backdrop-blur-sm border border-gray-100 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-transparent"></div>

                <CardContent className="p-6 relative">
                  <div className="flex items-start space-x-4 mb-5">
                    <div className={`relative w-14 h-14 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                      <feature.icon className="w-7 h-7 text-white" />
                      {/* Pulse effect */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} rounded-2xl animate-pulse opacity-20`}></div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {feature.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
                        <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mr-3 flex-shrink-0 group-hover:scale-125 transition-transform duration-300"></div>
                        <span className="font-medium">{detail}</span>
                      </div>
                    ))}
                  </div>

                  {/* Animated border */}
                  <div className={`absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r ${feature.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out`}></div>

                  {/* Success indicator */}
                  <div className="absolute top-4 right-4 w-6 h-6 bg-gradient-to-br from-green-400 to-emerald-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Customer Reviews Section */}
      <section className="py-16 bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 relative">
        {/* Subtle background elements */}
        <div className="absolute top-10 right-10 w-20 h-20 bg-gradient-to-br from-primary-200/20 to-blue-200/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-gradient-to-br from-accent-200/20 to-primary-200/20 rounded-full blur-xl"></div>

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
              ⭐ 4.9/5 sterren • 500+ tevreden klanten
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              Wat onze <span className="text-primary-600">klanten</span> zeggen
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Ontdek waarom klanten ons aanbevelen en steeds terugkomen
            </p>
          </div>

          {/* Reviews Carousel Container */}
          <div className="relative overflow-hidden">
            <div
              className="flex transition-transform duration-1000 ease-in-out"
              style={{ transform: `translateX(-${currentReviewIndex * (100 / 3)}%)` }}
            >
              {reviews.map((review, index) => (
                <div key={index} className="w-1/3 flex-shrink-0 px-3">
                  <Card className="group relative overflow-hidden bg-white/90 backdrop-blur-sm border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 h-full">
                    <CardContent className="p-6 relative h-full flex flex-col">
                      {/* Star rating */}
                      <div className="flex items-center mb-4">
                        <div className="flex space-x-1">
                          {[...Array(review.rating)].map((_, i) => (
                            <span key={i} className="text-accent-500 text-lg">★</span>
                          ))}
                        </div>
                        <span className="ml-2 text-sm font-medium text-gray-600">{review.rating}.0</span>
                      </div>

                      {/* Review text */}
                      <blockquote className="text-gray-700 mb-6 leading-relaxed italic flex-grow">
                        "{review.text}"
                      </blockquote>

                      {/* Customer info */}
                      <div className="flex items-center mt-auto">
                        <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                          {review.name.charAt(0)}
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{review.name}</p>
                          <p className="text-sm text-gray-500">Tevreden klant</p>
                        </div>
                      </div>

                      {/* Hover accent */}
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 to-accent-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>

          {/* Carousel Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: reviews.length - 2 }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentReviewIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentReviewIndex
                    ? 'bg-primary-600 scale-125'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link to="/beoordelingen">
              <Button variant="outline" className="border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg">
                Meer beoordelingen
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Popular Jobs Section */}
      <section className="py-16 bg-gradient-to-br from-white via-gray-50/50 to-slate-50 relative">
        {/* Floating elements */}
        <div className="absolute top-10 right-10 w-24 h-24 bg-gradient-to-br from-accent-200/20 to-orange-200/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-gradient-to-br from-primary-200/20 to-blue-200/20 rounded-full blur-xl"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-accent-100 to-orange-100 text-accent-700 rounded-full text-sm font-medium mb-6">
              🔥 Meest gevraagde klussen
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              Populaire <span className="bg-gradient-to-r from-accent-600 to-orange-600 bg-clip-text text-transparent">klussen in de buurt</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Ontdek de meest aangevraagde klussen in uw omgeving met transparante prijzen
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[
              {
                title: "Kraan laten plaatsen of vervangen",
                requests: "16.714 keer aangevraagd",
                price: "€75",
                image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop",
                icon: Droplets,
                iconBg: "from-blue-100 to-cyan-100",
                iconColor: "text-blue-600"
              },
              {
                title: "Lekkende kraan laten repareren",
                requests: "6.336 keer aangevraagd",
                price: "€65",
                image: "https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=300&h=200&fit=crop",
                icon: Wrench,
                iconBg: "from-orange-100 to-red-100",
                iconColor: "text-orange-600"
              },
              {
                title: "Toiletpot laten plaatsen of vervangen",
                requests: "3.618 keer aangevraagd",
                price: "€199",
                image: "https://images.unsplash.com/photo-1584622650111-993a426fbf0a?w=300&h=200&fit=crop",
                icon: Home,
                iconBg: "from-green-100 to-emerald-100",
                iconColor: "text-green-600"
              },
              {
                title: "Afvoer laten ontstoppen",
                requests: "10.422 keer aangevraagd",
                price: "€109",
                image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=300&h=200&fit=crop",
                icon: Settings,
                iconBg: "from-purple-100 to-violet-100",
                iconColor: "text-purple-600"
              },
              {
                title: "Gordijnrail laten monteren",
                requests: "2.895 keer aangevraagd",
                price: "€49",
                image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop",
                icon: Hammer,
                iconBg: "from-amber-100 to-yellow-100",
                iconColor: "text-amber-600"
              },
              {
                title: "Lamp laten ophangen",
                requests: "11.936 keer aangevraagd",
                price: "€55",
                image: "https://images.unsplash.com/photo-1524484485831-a92ffc0de03f?w=300&h=200&fit=crop",
                icon: Lightbulb,
                iconBg: "from-yellow-100 to-orange-100",
                iconColor: "text-yellow-600"
              },
              {
                title: "TV laten ophangen",
                requests: "3.495 keer aangevraagd",
                price: "€89",
                image: "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=300&h=200&fit=crop",
                icon: Monitor,
                iconBg: "from-indigo-100 to-blue-100",
                iconColor: "text-indigo-600"
              },
              {
                title: "Kast laten monteren",
                requests: "3.544 keer aangevraagd",
                price: "€90",
                image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop",
                icon: Building,
                iconBg: "from-slate-100 to-gray-100",
                iconColor: "text-slate-600"
              }
            ].map((job, index) => {
              const IconComponent = job.icon;
              return (
                <div key={index} className="group relative overflow-hidden bg-white/90 backdrop-blur-sm border border-gray-100 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                  {/* Image */}
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={job.image}
                      alt={job.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

                    {/* Icon */}
                    <div className={`absolute top-4 left-4 w-12 h-12 bg-gradient-to-br ${job.iconBg} backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-all duration-300`}>
                      <IconComponent className={`w-6 h-6 ${job.iconColor}`} />
                    </div>

                    {/* Price Badge */}
                    <div className="absolute top-4 right-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">
                      Vanaf {job.price}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300">
                      {job.title}
                    </h3>

                    <div className="flex items-center text-sm text-gray-600 mb-4">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      <span>{job.requests}</span>
                    </div>

                    <Button className="w-full bg-gradient-to-r from-primary-600 to-blue-600 hover:from-primary-700 hover:to-blue-700 text-white rounded-xl font-medium transition-all duration-300 transform group-hover:scale-105">
                      Boek een vakman →
                    </Button>
                  </div>

                  {/* Hover accent */}
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-accent-500 to-orange-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out"></div>
                </div>
              );
            })}
          </div>

          <div className="text-center">
            <Link to="/klus-plaatsen">
              <Button className="bg-gradient-to-r from-accent-600 to-orange-600 hover:from-accent-700 hover:to-orange-700 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
                Alle populaire klussen bekijken
                <span className="ml-2">→</span>
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-20 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 to-blue-100/30"></div>
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.3'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '40px 40px'
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-6">
              🔧 Professionele vakmannen voor elke klus
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Onze <span className="bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">Diensten</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Van kleine reparaties tot grote projecten - ontdek ons uitgebreide aanbod van professionele diensten
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <Link key={index} to={service.link}>
                  <Card className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 cursor-pointer">
                    {/* Glassmorphism effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>

                    <CardContent className="p-6 text-center relative">
                      <div className="mb-4 flex justify-center">
                        <div className={`relative w-14 h-14 bg-gradient-to-br ${service.iconBg} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                          <IconComponent className={`w-7 h-7 ${service.iconColor} transition-colors duration-300`} />
                          {/* Glow effect */}
                          <div className={`absolute inset-0 bg-gradient-to-br ${service.iconBg} rounded-2xl blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-300`}></div>
                        </div>
                      </div>
                      <h3 className="text-sm font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">{service.title}</h3>
                      <p className="text-xs text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">{service.description}</p>

                      {/* Animated border */}
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 to-blue-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ease-out"></div>

                      {/* Corner accent */}
                      <div className="absolute top-2 right-2 w-3 h-3 bg-gradient-to-br from-primary-400/30 to-blue-400/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
          <div className="text-center mt-12">
            <Link to="/diensten">
              <Button className="bg-gradient-to-r from-primary-600 to-blue-600 hover:from-primary-700 hover:to-blue-700 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
                Alle diensten bekijken
                <span className="ml-2">→</span>
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Service Area Section */}
      <section className="py-20 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 to-blue-100/30"></div>
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary-100 to-blue-100 text-primary-700 rounded-full text-sm font-medium mb-6">
              📍 Werkgebied & Voorrijkosten
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              Ons <span className="bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">Werkgebied</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              Onze diensten zijn beschikbaar vanuit Mijdrecht en bestrijken een gebied van ongeveer 100 km eromheen.
              Dit omvat de provincies Noord-Holland, Utrecht, Zuid-Holland, en de bijbehorende steden en dorpen.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Postcode Search & Calculator */}
            <div className="space-y-8">
              <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 shadow-xl">
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-blue-500 rounded-xl flex items-center justify-center mr-4">
                      <Search className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">Postcode Checker</h3>
                      <p className="text-gray-600">Controleer of wij bij u werken</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Voer uw postcode of plaatsnaam in
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="text"
                          value={postcode}
                          onChange={(e) => setPostcode(e.target.value)}
                          placeholder="bijv. 3641 of Amsterdam"
                          className="flex-1 px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                          onKeyPress={(e) => e.key === 'Enter' && handlePostcodeSearch()}
                        />
                        <Button
                          onClick={handlePostcodeSearch}
                          disabled={isLoading}
                          className="bg-gradient-to-r from-primary-600 to-blue-600 hover:from-primary-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50"
                        >
                          {isLoading ? 'Zoeken...' : 'Zoeken'}
                        </Button>
                      </div>
                    </div>

                    {searchResult && (
                      <div className={`p-4 rounded-xl border-l-4 ${
                        searchResult.inServiceArea
                          ? 'bg-green-50 border-green-500'
                          : 'bg-red-50 border-red-500'
                      }`}>
                        <div className="flex items-start">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                            searchResult.inServiceArea
                              ? 'bg-green-100 text-green-600'
                              : 'bg-red-100 text-red-600'
                          }`}>
                            {searchResult.inServiceArea ? '✓' : '✗'}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-1">
                              {searchResult.location}
                            </h4>
                            <p className="text-sm text-gray-600 mb-2">
                              Afstand vanaf Mijdrecht: {searchResult.distance}
                            </p>
                            {searchResult.inServiceArea && searchResult.cost !== null && (
                              <div className="bg-white rounded-lg p-3 mb-2 border">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium text-gray-700">Voorrijkosten:</span>
                                  <span className={`font-bold ${searchResult.cost === 0 ? 'text-green-600' : 'text-orange-600'}`}>
                                    {searchResult.cost === 0 ? 'Gratis!' : `€${searchResult.cost}`}
                                  </span>
                                </div>
                                {searchResult.cost > 0 && (
                                  <p className="text-xs text-gray-500 mt-1">excl. BTW</p>
                                )}
                              </div>
                            )}
                            <p className={`font-medium ${
                              searchResult.inServiceArea ? 'text-green-700' : 'text-red-700'
                            }`}>
                              {searchResult.message}
                            </p>

                            {/* Google Maps embed */}
                            {searchResult.coordinates && (
                              <div className="mt-4">
                                <div className="bg-white rounded-lg p-3 border">
                                  <h5 className="text-sm font-medium text-gray-700 mb-2">Locatie op kaart:</h5>
                                  <div className="w-full h-48 rounded-lg overflow-hidden">
                                    <iframe
                                      src={GOOGLE_MAPS_API_KEY
                                        ? `https://www.google.com/maps/embed/v1/directions?key=${GOOGLE_MAPS_API_KEY}&origin=Mijdrecht,Netherlands&destination=${searchResult.coordinates.lat},${searchResult.coordinates.lng}&mode=driving`
                                        : `https://www.google.com/maps?q=${searchResult.coordinates.lat},${searchResult.coordinates.lng}&output=embed&z=10`
                                      }
                                      width="100%"
                                      height="100%"
                                      style={{ border: 0 }}
                                      allowFullScreen=""
                                      loading="lazy"
                                      referrerPolicy="no-referrer-when-downgrade"
                                      className="rounded-lg"
                                    ></iframe>
                                  </div>
                                  <p className="text-xs text-gray-500 mt-2">
                                    Route van Mijdrecht naar uw locatie
                                  </p>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Travel Costs Calculator */}
              <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 shadow-xl">
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-accent-500 to-orange-500 rounded-xl flex items-center justify-center mr-4">
                      <Calculator className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">Voorrijkosten Calculator</h3>
                      <p className="text-gray-600">Transparante prijzen vooraf</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600 mb-1">€0</div>
                          <div className="text-sm text-green-700 font-medium">&lt; 10 km</div>
                          <div className="text-xs text-green-600 mt-1">Geen voorrijkosten</div>
                        </div>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-yellow-600 mb-1">€25</div>
                          <div className="text-sm text-yellow-700 font-medium">10-25 km</div>
                          <div className="text-xs text-yellow-600 mt-1">excl. BTW</div>
                        </div>
                      </div>
                      <div className="p-4 bg-orange-50 rounded-xl border border-orange-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600 mb-1">€50</div>
                          <div className="text-sm text-orange-700 font-medium">25-50 km</div>
                          <div className="text-xs text-orange-600 mt-1">excl. BTW</div>
                        </div>
                      </div>
                      <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-red-600 mb-1">€75</div>
                          <div className="text-sm text-red-700 font-medium">50-100 km</div>
                          <div className="text-xs text-red-600 mt-1">excl. BTW</div>
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 text-center">
                      * Alle prijzen zijn exclusief BTW
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Service Areas List */}
            <div className="space-y-6">
              {/* Google Maps Overview */}
              <Card className="bg-white/90 backdrop-blur-sm border border-gray-100 shadow-xl">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mr-4">
                      <Map className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">Ons Werkgebied</h3>
                      <p className="text-gray-600">100 km rondom Mijdrecht</p>
                    </div>
                  </div>

                  <div className="w-full h-80 rounded-xl overflow-hidden border border-gray-200">
                    <iframe
                      src={GOOGLE_MAPS_API_KEY
                        ? `https://www.google.com/maps/embed/v1/view?key=${GOOGLE_MAPS_API_KEY}&center=52.2067,4.8647&zoom=8&maptype=roadmap`
                        : "https://www.google.com/maps?q=Mijdrecht,Netherlands&output=embed&z=8"
                      }
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      allowFullScreen=""
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      className="rounded-xl"
                    ></iframe>
                  </div>

                  <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-gray-600">0-10 km: Gratis</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                      <span className="text-gray-600">10-25 km: €25</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                      <span className="text-gray-600">25-50 km: €50</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                      <span className="text-gray-600">50-100 km: €75</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
              {Object.entries(serviceAreas).map(([province, cities]) => (
                <Card key={province} className="bg-white/90 backdrop-blur-sm border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <Map className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="text-lg font-bold text-gray-900">{province}</h3>
                    </div>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                      {cities.map((city, index) => (
                        <div key={index} className="text-sm text-gray-600 hover:text-primary-600 transition-colors duration-200 cursor-pointer">
                          {city}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div className="text-center mt-12">
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-100 to-blue-100 text-primary-700 rounded-xl text-sm font-medium">
              📞 Twijfelt u of wij bij u werken? Bel ons gerust: <strong className="ml-1">06-12345678</strong>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
