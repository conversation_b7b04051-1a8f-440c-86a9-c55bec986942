import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Droplets, Clock, Shield, Star, CheckCircle, Phone, Mail, MapPin, Wrench, Home, Zap } from 'lucide-react';

const Loodgieter = () => {
  const services = [
    {
      title: "Lekkage Reparatie",
      description: "Snelle reparatie van leidinglekkages",
      price: "vanaf €65",
      duration: "30-60 min",
      urgency: "Spoed mogelijk"
    },
    {
      title: "<PERSON><PERSON><PERSON> Installatie",
      description: "Installatie van nieuwe kranen en mengkranen",
      price: "vanaf €85",
      duration: "1-2 uur",
      urgency: "Zelfde dag"
    },
    {
      title: "Toilet Reparatie",
      description: "Reparatie en vervanging van toiletten",
      price: "vanaf €95",
      duration: "1-3 uur",
      urgency: "Binnen 24u"
    },
    {
      title: "CV-Ketel Onderhoud",
      description: "Onderhoud en reparatie van CV-ketels",
      price: "vanaf €125",
      duration: "2-3 uur",
      urgency: "Gepland"
    },
    {
      title: "Badkamer Installatie",
      description: "Complete badkamer leidingwerk",
      price: "op maat",
      duration: "1-3 dagen",
      urgency: "Planning"
    },
    {
      title: "Verstoppingen",
      description: "Ontstoppen van afvoeren en leidingen",
      price: "vanaf €75",
      duration: "30-90 min",
      urgency: "Spoed mogelijk"
    }
  ];

  const features = [
    "24/7 spoedservice beschikbaar",
    "Gecertificeerde loodgieters",
    "Transparante prijzen vooraf",
    "Garantie op alle werkzaamheden",
    "Moderne apparatuur en technieken",
    "Opruimen na de klus inbegrepen"
  ];

  const reviews = [
    {
      name: "Maria S.",
      rating: 5,
      comment: "Snelle service bij lekkage in de keuken. Zeer tevreden!",
      date: "2 weken geleden"
    },
    {
      name: "Piet J.",
      rating: 5,
      comment: "Professionele installatie van nieuwe kraan. Netjes uitgevoerd.",
      date: "1 maand geleden"
    },
    {
      name: "Lisa K.",
      rating: 4,
      comment: "Goed werk aan CV-ketel. Op tijd en binnen budget.",
      date: "3 weken geleden"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-cyan-500/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-6">
                <Droplets className="w-4 h-4 mr-2" />
                Professionele Loodgietersservice
              </div>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Loodgieter
                <span className="text-blue-600"> Diensten</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Van spoedlekkages tot complete badkamerinstallaties - onze gecertificeerde loodgieters 
                staan 24/7 voor u klaar. Betrouwbaar, snel en tegen eerlijke prijzen.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/afspraak-maken">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    <Phone className="w-5 h-5 mr-2" />
                    Direct Bellen
                  </Button>
                </Link>
                <Link to="/offerte">
                  <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                    Gratis Offerte
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 border border-white/30">
                <img
                  src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop"
                  alt="Professionele loodgieter aan het werk"
                  className="w-full h-80 object-cover rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Onze <span className="text-blue-600">Loodgieter</span> Diensten
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Professionele loodgietersklussen uitgevoerd door ervaren vakmannen
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-cyan-100 rounded-xl flex items-center justify-center">
                      <Wrench className="w-6 h-6 text-blue-600" />
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      service.urgency === 'Spoed mogelijk' ? 'bg-red-100 text-red-700' :
                      service.urgency === 'Zelfde dag' ? 'bg-orange-100 text-orange-700' :
                      service.urgency === 'Binnen 24u' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {service.urgency}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {service.duration}
                    </div>
                    <div className="font-semibold text-blue-600">{service.price}</div>
                  </div>
                  
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white">
                    Boek Nu
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Waarom kiezen voor onze <span className="text-blue-600">loodgieters</span>?
              </h2>
              <div className="space-y-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700 font-medium">{feature}</span>
                  </div>
                ))}
              </div>
              <div className="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="font-bold text-blue-800 mb-2">Spoedservice 24/7</h3>
                <p className="text-blue-700">
                  Heeft u een spoedlekkage of andere urgente loodgieterreparatie? 
                  Bel onze 24/7 spoedlijn: <strong>06-12345678</strong>
                </p>
              </div>
            </div>
            <div>
              <img 
                src="https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=600&h=400&fit=crop"
                alt="Loodgieter tools en materialen"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Wat onze <span className="text-blue-600">klanten</span> zeggen
            </h2>
            <div className="flex items-center justify-center mb-4">
              <div className="flex space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="ml-2 text-lg font-semibold text-gray-700">4.9/5 (127 beoordelingen)</span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {reviews.map((review, index) => (
              <Card key={index} className="bg-white shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="flex space-x-1">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-gray-500">{review.date}</span>
                  </div>
                  <p className="text-gray-700 mb-4 italic">"{review.comment}"</p>
                  <p className="font-semibold text-gray-900">{review.name}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-cyan-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Heeft u een loodgieter nodig?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Bel nu voor een gratis offerte of spoedservice
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/afspraak-maken">
              <Button className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105">
                <Phone className="w-5 h-5 mr-2" />
                Bel Nu: 06-12345678
              </Button>
            </Link>
            <Link to="/offerte">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                <Mail className="w-5 h-5 mr-2" />
                Vraag Offerte Aan
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Loodgieter;
