import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Truck } from 'lucide-react';

const Klushulp = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-700 rounded-full text-sm font-medium mb-6">
            <Truck className="w-4 h-4 mr-2" />
            Klushulp Specialisten
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Klushulp <span className="text-orange-600">Diensten</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Verhuizing en transport hulp door ervaren krachten. 
            Van kleine verhuizingen tot grote transportklussen.
          </p>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-orange-800 mb-2">Binnenkort beschikbaar</h3>
            <p className="text-orange-700">
              Deze uitgebreide pagina wordt binnenkort gelanceerd met alle informatie over onze klushulp diensten.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Klushulp;
