import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Zap, Clock, Shield, Star, CheckCircle, Phone, Mail, AlertTriangle, Lightbulb, Home, Wifi } from 'lucide-react';

const Elektricien = () => {
  const services = [
    {
      title: "Stroomstoring Reparatie",
      description: "Snelle oplossing voor stroomstoringen",
      price: "vanaf €75",
      duration: "30-90 min",
      urgency: "Spoed mogelijk"
    },
    {
      title: "Stopcontact Installatie",
      description: "Nieuwe stopcontacten en schakelaars",
      price: "vanaf €65",
      duration: "1-2 uur",
      urgency: "Zelfde dag"
    },
    {
      title: "Verlichting Installatie",
      description: "LED verlichting en lampen installeren",
      price: "vanaf €85",
      duration: "1-3 uur",
      urgency: "Binnen 24u"
    },
    {
      title: "Meterkast Vernieuwing",
      description: "Moderne meterkast installatie",
      price: "vanaf €350",
      duration: "4-6 uur",
      urgency: "Gepland"
    },
    {
      title: "Periodieke Keuring",
      description: "Wettelijk verplichte elektrische keuring",
      price: "vanaf €150",
      duration: "2-3 uur",
      urgency: "Gepland"
    },
    {
      title: "Smart Home Installatie",
      description: "Moderne domotica systemen installeren",
      price: "op maat",
      duration: "1-2 dagen",
      urgency: "Planning"
    }
  ];

  const features = [
    "Gecertificeerde elektriciens",
    "Veiligheid staat voorop",
    "Keuringscertificaten afgegeven",
    "Smart home specialisten",
    "24/7 spoedservice bij stroomuitval",
    "Garantie op alle werkzaamheden"
  ];

  const reviews = [
    {
      name: "Jan D.",
      rating: 5,
      comment: "Snelle hulp bij stroomstoring. Professioneel en betrouwbaar!",
      date: "1 week geleden"
    },
    {
      name: "Sarah M.",
      rating: 5,
      comment: "Perfecte installatie van nieuwe verlichting. Zeer tevreden.",
      date: "2 weken geleden"
    },
    {
      name: "Rob K.",
      rating: 5,
      comment: "Meterkast vernieuwd. Netjes uitgevoerd en goed uitgelegd.",
      date: "1 maand geleden"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-yellow-600/10 to-orange-500/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-700 rounded-full text-sm font-medium mb-6">
                <Zap className="w-4 h-4 mr-2" />
                Gecertificeerde Elektriciens
              </div>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Elektricien
                <span className="text-yellow-600"> Diensten</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Van spoedstroomstoringen tot complete elektrische installaties - onze gecertificeerde 
                elektriciens zorgen voor veilige en betrouwbare elektriciteit in uw woning of bedrijf.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/afspraak-maken">
                  <Button className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    <Phone className="w-5 h-5 mr-2" />
                    Spoedservice
                  </Button>
                </Link>
                <Link to="/offerte">
                  <Button variant="outline" className="border-yellow-600 text-yellow-600 hover:bg-yellow-600 hover:text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                    Gratis Offerte
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 border border-white/30">
                <img
                  src="https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=600&h=400&fit=crop"
                  alt="Professionele elektricien aan het werk"
                  className="w-full h-80 object-cover rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-gradient-to-br from-slate-50 via-yellow-50/30 to-orange-50/40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Onze <span className="text-yellow-600">Elektricien</span> Diensten
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Veilige en professionele elektrische installaties door gecertificeerde elektriciens
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-100 to-orange-100 rounded-xl flex items-center justify-center">
                      <Zap className="w-6 h-6 text-yellow-600" />
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      service.urgency === 'Spoed mogelijk' ? 'bg-red-100 text-red-700' :
                      service.urgency === 'Zelfde dag' ? 'bg-orange-100 text-orange-700' :
                      service.urgency === 'Binnen 24u' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {service.urgency}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-yellow-600 transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {service.duration}
                    </div>
                    <div className="font-semibold text-yellow-600">{service.price}</div>
                  </div>
                  
                  <Button className="w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white">
                    Boek Nu
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Safety Warning */}
      <section className="py-8 bg-red-50 border-y border-red-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center">
            <AlertTriangle className="w-8 h-8 text-red-600 mr-4" />
            <div className="text-center">
              <h3 className="text-lg font-bold text-red-800 mb-1">Veiligheid Waarschuwing</h3>
              <p className="text-red-700">
                Elektrisch werk kan gevaarlijk zijn. Laat dit altijd over aan gecertificeerde elektriciens voor uw veiligheid.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Waarom kiezen voor onze <span className="text-yellow-600">elektriciens</span>?
              </h2>
              <div className="space-y-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700 font-medium">{feature}</span>
                  </div>
                ))}
              </div>
              <div className="mt-8 p-6 bg-yellow-50 rounded-lg border border-yellow-200">
                <h3 className="font-bold text-yellow-800 mb-2">24/7 Spoedservice</h3>
                <p className="text-yellow-700">
                  Stroomstoring of andere elektrische noodgevallen? 
                  Bel onze 24/7 spoedlijn: <strong>06-12345678</strong>
                </p>
              </div>
            </div>
            <div>
              <img 
                src="https://images.unsplash.com/photo-1621905252507-b35492cc74b4?w=600&h=400&fit=crop"
                alt="Elektrische installatie en gereedschap"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Wat onze <span className="text-yellow-600">klanten</span> zeggen
            </h2>
            <div className="flex items-center justify-center mb-4">
              <div className="flex space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="ml-2 text-lg font-semibold text-gray-700">4.9/5 (89 beoordelingen)</span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {reviews.map((review, index) => (
              <Card key={index} className="bg-white shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="flex space-x-1">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-gray-500">{review.date}</span>
                  </div>
                  <p className="text-gray-700 mb-4 italic">"{review.comment}"</p>
                  <p className="font-semibold text-gray-900">{review.name}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-yellow-600 to-orange-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Heeft u een elektricien nodig?
          </h2>
          <p className="text-xl text-yellow-100 mb-8">
            Bel nu voor spoedservice of een gratis offerte
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/afspraak-maken">
              <Button className="bg-white text-yellow-600 hover:bg-gray-100 px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105">
                <Phone className="w-5 h-5 mr-2" />
                Bel Nu: 06-12345678
              </Button>
            </Link>
            <Link to="/offerte">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-yellow-600 px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                <Mail className="w-5 h-5 mr-2" />
                Vraag Offerte Aan
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Elektricien;
