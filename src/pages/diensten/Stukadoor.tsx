import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Wrench } from 'lucide-react';

const Stukadoor = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium mb-6">
            <Wrench className="w-4 h-4 mr-2" />
            Professionele Stukadoors
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Stukadoor <span className="text-gray-600">Diensten</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Pleisterwerk en afwerking door ervaren stukadoors. 
            <PERSON> kleine reparaties tot complete wandafwerking.
          </p>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Binnenkort beschikbaar</h3>
            <p className="text-gray-700">
              Deze uitgebreide pagina wordt binnenkort gelanceerd met alle informatie over onze stukadoor diensten.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Stukadoor;
