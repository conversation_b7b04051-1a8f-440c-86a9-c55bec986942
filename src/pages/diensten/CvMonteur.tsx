import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Thermometer, Clock, Shield, Star, CheckCircle, Phone, Mail } from 'lucide-react';

const CvMonteur = () => {
  const services = [
    {
      title: "CV-Ketel Onderhoud",
      description: "Jaarlijks onderhoud voor optimale werking",
      price: "vanaf €125",
      duration: "2-3 uur",
      urgency: "Gepland"
    },
    {
      title: "CV-Ketel Reparatie",
      description: "Snelle reparatie van defecte ketels",
      price: "vanaf €95",
      duration: "1-3 uur",
      urgency: "Zelfde dag"
    },
    {
      title: "Radiator Installatie",
      description: "Nieuwe radiatoren plaatsen en aansluiten",
      price: "vanaf €185",
      duration: "2-4 uur",
      urgency: "Binnen 48u"
    },
    {
      title: "Thermostaat Installatie",
      description: "Slimme thermostaten installeren",
      price: "vanaf €85",
      duration: "1-2 uur",
      urgency: "Binnen 24u"
    },
    {
      title: "Leidingwerk CV",
      description: "CV-leidingen aanleggen en repareren",
      price: "op maat",
      duration: "4-8 uur",
      urgency: "Planning"
    },
    {
      title: "Spoedservice Verwarming",
      description: "24/7 service bij verwarmingsproblemen",
      price: "vanaf €150",
      duration: "1-2 uur",
      urgency: "Spoed mogelijk"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-red-600/10 to-orange-500/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center px-4 py-2 bg-red-100 text-red-700 rounded-full text-sm font-medium mb-6">
                <Thermometer className="w-4 h-4 mr-2" />
                Gecertificeerde CV-Monteurs
              </div>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                CV Monteur
                <span className="text-red-600"> Diensten</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Van CV-ketel onderhoud tot complete verwarmingsinstallaties - onze gecertificeerde 
                CV-monteurs zorgen voor optimale verwarming in uw woning. 24/7 spoedservice beschikbaar.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/afspraak-maken">
                  <Button className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    <Phone className="w-5 h-5 mr-2" />
                    Spoedservice
                  </Button>
                </Link>
                <Link to="/offerte">
                  <Button variant="outline" className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                    Gratis Offerte
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 border border-white/30">
                <img
                  src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop"
                  alt="CV-monteur aan het werk aan verwarmingsketel"
                  className="w-full h-80 object-cover rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-gradient-to-br from-slate-50 via-red-50/30 to-orange-50/40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Onze <span className="text-red-600">CV-Monteur</span> Diensten
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Professionele verwarming en klimaatbeheersing door gecertificeerde monteurs
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-red-100 to-orange-100 rounded-xl flex items-center justify-center">
                      <Thermometer className="w-6 h-6 text-red-600" />
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      service.urgency === 'Spoed mogelijk' ? 'bg-red-100 text-red-700' :
                      service.urgency === 'Zelfde dag' ? 'bg-orange-100 text-orange-700' :
                      service.urgency === 'Binnen 24u' ? 'bg-yellow-100 text-yellow-700' :
                      service.urgency === 'Binnen 48u' ? 'bg-blue-100 text-blue-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {service.urgency}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-red-600 transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {service.duration}
                    </div>
                    <div className="font-semibold text-red-600">{service.price}</div>
                  </div>
                  
                  <Button className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white">
                    Boek Nu
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-red-600 to-orange-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Problemen met uw verwarming?
          </h2>
          <p className="text-xl text-red-100 mb-8">
            Bel nu voor spoedservice of onderhoud van uw CV-installatie
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/afspraak-maken">
              <Button className="bg-white text-red-600 hover:bg-gray-100 px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105">
                <Phone className="w-5 h-5 mr-2" />
                Bel Nu: 06-12345678
              </Button>
            </Link>
            <Link to="/offerte">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-red-600 px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                <Mail className="w-5 h-5 mr-2" />
                Vraag Offerte Aan
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default CvMonteur;
