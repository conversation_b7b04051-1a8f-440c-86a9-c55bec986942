import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Hammer, Clock, Shield, Star, CheckCircle, Phone, Mail, Wrench, Home, Settings } from 'lucide-react';

const Klusjesman = () => {
  const services = [
    {
      title: "Kleine Reparaties",
      description: "Deur, raam en meubelreparaties",
      price: "vanaf €45",
      duration: "30-90 min",
      urgency: "Zelfde dag"
    },
    {
      title: "Ophangen & Monteren",
      description: "Schil<PERSON>ijen, planken, TV's ophangen",
      price: "vanaf €35",
      duration: "30-60 min",
      urgency: "Zelfde dag"
    },
    {
      title: "Meubel Montage",
      description: "IKEA en andere meubels in elkaar zetten",
      price: "vanaf €55",
      duration: "1-3 uur",
      urgency: "Binnen 24u"
    },
    {
      title: "Keuken Reparaties",
      description: "<PERSON><PERSON><PERSON><PERSON>, lades en werkblad reparaties",
      price: "vanaf €65",
      duration: "1-2 uur",
      urgency: "Binnen 24u"
    },
    {
      title: "Badkamer Klussen",
      description: "Kleine badkamer reparaties en onderhoud",
      price: "vanaf €75",
      duration: "1-3 uur",
      urgency: "Binnen 48u"
    },
    {
      title: "Algemeen Onderhoud",
      description: "Periodiek onderhoud en preventie",
      price: "vanaf €50",
      duration: "2-4 uur",
      urgency: "Gepland"
    }
  ];

  const features = [
    "Ervaren klusjesmannen",
    "Eigen gereedschap en materiaal",
    "Flexibele planning",
    "Eerlijke uurtarieven",
    "Opruimen na de klus",
    "Garantie op uitgevoerd werk"
  ];

  const reviews = [
    {
      name: "Emma V.",
      rating: 5,
      comment: "Perfecte montage van onze nieuwe kast. Snel en netjes!",
      date: "3 dagen geleden"
    },
    {
      name: "Tom B.",
      rating: 5,
      comment: "Kleine reparaties in huis. Zeer tevreden met het resultaat.",
      date: "1 week geleden"
    },
    {
      name: "Linda H.",
      rating: 5,
      comment: "TV opgehangen en kabels weggewerkt. Professioneel werk!",
      date: "2 weken geleden"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="relative pt-20 pb-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-orange-600/10 to-red-500/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-700 rounded-full text-sm font-medium mb-6">
                <Hammer className="w-4 h-4 mr-2" />
                Professionele Klusjesmannen
              </div>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Klusjesman
                <span className="text-orange-600"> Diensten</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Van kleine reparaties tot meubelmontage - onze ervaren klusjesmannen 
                pakken elke klus vakkundig aan. Snel, betrouwbaar en tegen eerlijke prijzen.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/afspraak-maken">
                  <Button className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    <Phone className="w-5 h-5 mr-2" />
                    Direct Bellen
                  </Button>
                </Link>
                <Link to="/offerte">
                  <Button variant="outline" className="border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                    Gratis Offerte
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/20 backdrop-blur-sm rounded-3xl p-8 border border-white/30">
                <img
                  src="https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=600&h=400&fit=crop"
                  alt="Klusjesman aan het werk met gereedschap"
                  className="w-full h-80 object-cover rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-gradient-to-br from-slate-50 via-orange-50/30 to-red-50/40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Onze <span className="text-orange-600">Klusjesman</span> Diensten
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Van kleine reparaties tot grote klussen - wij pakken het allemaal aan
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => (
              <Card key={index} className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-red-100 rounded-xl flex items-center justify-center">
                      <Hammer className="w-6 h-6 text-orange-600" />
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      service.urgency === 'Zelfde dag' ? 'bg-orange-100 text-orange-700' :
                      service.urgency === 'Binnen 24u' ? 'bg-yellow-100 text-yellow-700' :
                      service.urgency === 'Binnen 48u' ? 'bg-blue-100 text-blue-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {service.urgency}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-orange-600 transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {service.duration}
                    </div>
                    <div className="font-semibold text-orange-600">{service.price}</div>
                  </div>
                  
                  <Button className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white">
                    Boek Nu
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Waarom kiezen voor onze <span className="text-orange-600">klusjesmannen</span>?
              </h2>
              <div className="space-y-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="w-6 h-6 text-green-500 mr-3 flex-shrink-0" />
                    <span className="text-gray-700 font-medium">{feature}</span>
                  </div>
                ))}
              </div>
              <div className="mt-8 p-6 bg-orange-50 rounded-lg border border-orange-200">
                <h3 className="font-bold text-orange-800 mb-2">Geen klus te klein</h3>
                <p className="text-orange-700">
                  Van het ophangen van een schilderij tot het repareren van een deur - 
                  wij pakken elke klus met dezelfde professionaliteit aan.
                </p>
              </div>
            </div>
            <div>
              <img 
                src="https://images.unsplash.com/photo-1504148455328-c376907d081c?w=600&h=400&fit=crop"
                alt="Gereedschap en klusmateriaal"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Wat onze <span className="text-orange-600">klanten</span> zeggen
            </h2>
            <div className="flex items-center justify-center mb-4">
              <div className="flex space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="ml-2 text-lg font-semibold text-gray-700">4.8/5 (156 beoordelingen)</span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {reviews.map((review, index) => (
              <Card key={index} className="bg-white shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="flex space-x-1">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-gray-500">{review.date}</span>
                  </div>
                  <p className="text-gray-700 mb-4 italic">"{review.comment}"</p>
                  <p className="font-semibold text-gray-900">{review.name}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-orange-600 to-red-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Heeft u een klusjesman nodig?
          </h2>
          <p className="text-xl text-orange-100 mb-8">
            Bel nu voor een gratis offerte of directe planning
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/afspraak-maken">
              <Button className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300 transform hover:scale-105">
                <Phone className="w-5 h-5 mr-2" />
                Bel Nu: 06-12345678
              </Button>
            </Link>
            <Link to="/offerte">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4 text-lg rounded-xl font-medium transition-all duration-300">
                <Mail className="w-5 h-5 mr-2" />
                Vraag Offerte Aan
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Klusjesman;
