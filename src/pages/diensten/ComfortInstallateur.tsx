import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Lightbulb } from 'lucide-react';

const ComfortInstallateur = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-amber-100 text-amber-700 rounded-full text-sm font-medium mb-6">
            <Lightbulb className="w-4 h-4 mr-2" />
            Comfort Installateurs
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Comfort Installateur <span className="text-amber-600">Diensten</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Comfort en energiebesparende systemen door specialisten. 
            Voor optimaal wooncomfort en lagere energiekosten.
          </p>
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-amber-800 mb-2">Binnenkort beschikbaar</h3>
            <p className="text-amber-700">
              Deze uitgebreide pagina wordt binnenkort gelanceerd met alle informatie over onze comfort installateur diensten.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ComfortInstallateur;
