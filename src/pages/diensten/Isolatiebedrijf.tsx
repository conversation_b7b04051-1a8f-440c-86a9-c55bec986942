import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Building } from 'lucide-react';

const Isolatiebedrijf = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-emerald-100 text-emerald-700 rounded-full text-sm font-medium mb-6">
            <Building className="w-4 h-4 mr-2" />
            Isolatie Specialisten
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Isolatiebedrijf <span className="text-emerald-600">Diensten</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Isolatie en energiebesparing door gecertificeerde specialisten. 
            Voor een energiezuinige en comfortabele woning.
          </p>
          <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-emerald-800 mb-2">Binnenkort beschikbaar</h3>
            <p className="text-emerald-700">
              Deze uitgebreide pagina wordt binnenkort gelanceerd met alle informatie over onze isolatie diensten.
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Isolatiebedrijf;
