# Google Maps API Setup

## 🗺️ Hoe Google Maps API Key configureren

### Stap 1: Google Cloud Console
1. Ga naar [Google Cloud Console](https://console.cloud.google.com/)
2. Maak een nieuw project of selecteer bestaand project
3. Zorg dat billing is ingeschakeld (gratis tier beschikbaar)

### Stap 2: APIs inschakelen
Ga naar "APIs & Services" > "Library" en schakel deze APIs in:
- **Maps Embed API** (voor embedded kaarten)
- **Geocoding API** (voor adres → coördinaten)
- **Maps JavaScript API** (voor interactieve kaarten)

### Stap 3: API Key maken
1. Ga naar "APIs & Services" > "Credentials"
2. <PERSON><PERSON> "Create Credentials" > "API Key"
3. Kopieer de gegenereerde key (bijv: `AIzaSyBFw0Qbyq9zTFTd-tUY6dO_BcqCGAOtMvs`)

### Stap 4: API Key beveiligen (aanbevolen)
1. Klik op de API key om te bewerken
2. Onder "Application restrictions":
   - <PERSON><PERSON> "HTTP referrers (web sites)"
   - Voeg toe: `localhost:8080/*` en `yourdomain.com/*`
3. Onder "API restrictions":
   - Selecteer "Restrict key"
   - Kies alleen de APIs die je gebruikt

### Stap 5: Configureren in project
1. Kopieer `.env.example` naar `.env`
2. Vervang `your_google_maps_api_key_here` met jouw echte API key:
   ```
   VITE_GOOGLE_MAPS_API_KEY=AIzaSyBFw0Qbyq9zTFTd-tUY6dO_BcqCGAOtMvs
   ```
3. Herstart de development server: `npm run dev`

## 🔧 Huidige implementatie

### Zonder API Key (huidige situatie):
- ✅ Basis Google Maps embeds werken
- ✅ Afstandsberekening werkt (OpenStreetMap geocoding)
- ❌ Geen route-weergave
- ❌ Beperkte kaart functionaliteit

### Met API Key:
- ✅ Volledige Google Maps functionaliteit
- ✅ Route-weergave van Mijdrecht naar klant
- ✅ Betere kaart kwaliteit
- ✅ Meer kaart opties en styling

## 💰 Kosten
- **Gratis tier**: 28.000 kaart loads per maand
- **Maps Embed API**: Gratis voor de meeste gebruik
- **Geocoding API**: $5 per 1000 requests na gratis tier

## 🚨 Belangrijk
- Bewaar je API key veilig (niet in git committen)
- Gebruik restrictions om misbruik te voorkomen
- Monitor je usage in Google Cloud Console

## 🔄 OAuth vs API Key
De string die je hebt gedeeld (`1096676392927-dk7mjqmermmq6acjqmi5kopf9kdf1hb9.apps.googleusercontent.com`) is een **OAuth Client ID**, niet een Maps API key. Voor Maps heb je een andere key nodig die begint met `AIza...`
