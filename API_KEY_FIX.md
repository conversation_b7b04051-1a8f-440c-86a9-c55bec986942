# 🔧 Google Maps API Key Restricties Oplossen

## ❌ Huidige Fout:
```
Google Maps Platform rejected your request. This IP, site or mobile application is not authorized to use this API key.
```

## ✅ Oplossing: API Key Restricties Aanpassen

### Stap 1: Google Cloud Console
1. Ga naar: https://console.cloud.google.com/
2. Selecteer je project
3. Navigeer naar: **APIs & Services** → **Credentials**

### Stap 2: API Key Bewerken
1. Klik op je API key: `AIzaSyCc-nd_cg4kLiKOBxKNDVFMBlB92iVljVI`
2. Klik op het potlood icoon om te bewerken

### Stap 3: Application Restrictions Aanpassen
1. Onder **"Application restrictions"**:
   - Selecteer: **"HTTP referrers (web sites)"**
   
2. Voeg deze referrers toe:
   ```
   localhost:8081/*
   127.0.0.1:8081/*
   http://localhost:8081/*
   localhost:8080/*
   127.0.0.1:8080/*
   http://localhost:8080/*
   ```

3. Voor productie voeg ook toe:
   ```
   yourdomain.com/*
   *.yourdomain.com/*
   ```

### Stap 4: API Restrictions (Optioneel)
1. Onder **"API restrictions"**:
   - Selecteer: **"Restrict key"**
   - Kies alleen:
     - Maps Embed API
     - Geocoding API
     - Maps JavaScript API

### Stap 5: Opslaan
1. Klik **"Save"**
2. Wacht 1-2 minuten voor propagatie
3. Refresh je website

## 🔄 Alternatieve Oplossing: Geen Restricties

**Tijdelijk voor development:**
1. Onder **"Application restrictions"**:
   - Selecteer: **"None"**
2. Klik **"Save"**

⚠️ **Waarschuwing**: Dit is minder veilig voor productie!

## 🧪 Test na Aanpassing

1. Refresh de website: http://localhost:8081/
2. Ga naar "Werkgebied" sectie
3. Test de postcode checker:
   - Klik "Test: Amsterdam"
   - Klik "Zoeken"
   - Controleer of de kaart laadt

## 📊 Huidige Fallback

De website werkt nu met:
- ✅ **Geocoding**: Google Maps API (werkt via server-side)
- ✅ **Afstandsberekening**: Haversine formule (werkt perfect)
- ✅ **Voorrijkosten**: Automatische berekening (werkt perfect)
- ⚠️ **Maps Embed**: Basis Google Maps (zonder API key features)

## 🎯 Na API Key Fix

Met werkende API key krijg je:
- 🗺️ **Premium kaarten**: Betere kwaliteit
- 🛣️ **Route weergave**: Turn-by-turn directions
- 🎨 **Custom styling**: Meer kaart opties
- ⚡ **Snellere loading**: Directe Google servers

## 💡 Tips

1. **Development**: Gebruik localhost restricties
2. **Staging**: Voeg staging domain toe
3. **Production**: Gebruik alleen productie domains
4. **Monitoring**: Check usage in Google Cloud Console
5. **Backup**: Houd altijd een fallback systeem

## 🔍 Debug

Als het nog niet werkt:
1. Check browser console voor errors
2. Controleer Network tab voor API calls
3. Verify API key in Google Cloud Console
4. Test met verschillende browsers
